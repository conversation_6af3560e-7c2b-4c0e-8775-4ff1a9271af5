//
//  IMYNetworkPerformanceMetrics.h
//  IMYPublic
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface IMYNetworkPerformanceMetrics : NSObject

@property (nonatomic, strong) NSString *requestId;
@property (nonatomic, strong) NSString *url;
@property (nonatomic, strong) NSString *method;
@property (nonatomic, assign) NSTimeInterval dns_lookup_time;
@property (nonatomic, assign) NSTimeInterval tcp_connect_time;
@property (nonatomic, assign) NSTimeInterval ssl_handshake_time;
@property (nonatomic, assign) NSTimeInterval request_send_time;
@property (nonatomic, assign) NSTimeInterval time_to_first_byte;
@property (nonatomic, assign) NSTimeInterval response_receive_time;
@property (nonatomic, assign) NSTimeInterval total_request_time;
@property (nonatomic, assign) NSInteger httpStatusCode;
@property (nonatomic, assign) NSInteger responseSizeBytes;
@property (nonatomic, strong, nullable) NSString *errorCode;
@property (nonatomic, strong, nullable) NSString *errorMessage;
@property (nonatomic, assign) NSTimeInterval timestampSec;

- (NSDictionary *)toDictionary;

@end

NS_ASSUME_NONNULL_END
