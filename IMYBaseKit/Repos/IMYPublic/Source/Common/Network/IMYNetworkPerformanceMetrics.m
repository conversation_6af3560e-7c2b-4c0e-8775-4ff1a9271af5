//
//  IMYNetworkPerformanceMetrics.m
//  IMYPublic
//

#import "IMYNetworkPerformanceMetrics.h"

@implementation IMYNetworkPerformanceMetrics

- (instancetype)init {
    if (self = [super init]) {
        _requestId = [[NSUUID UUID] UUIDString];
        _timestampSec = [[NSDate date] timeIntervalSince1970];
        _url = @"";
        _method = @"";
        _errorCode = @"";
        _errorMessage = @"";
        _dns_lookup_time = 0;
        _tcp_connect_time = 0;
        _ssl_handshake_time = 0;
        _request_send_time = 0;
        _time_to_first_byte = 0;
        _response_receive_time = 0;
        _total_request_time = 0;
        _httpStatusCode = 0;
        _responseSizeBytes = 0;
    }
    return self;
}

- (NSDictionary *)toDictionary {
    return @{
        @"request_id": self.requestId ?: @"",
        @"url": self.url ?: @"",
        @"method": self.method ?: @"",
        @"timings_ms": @{
            @"dns_lookup_duration": @(self.dns_lookup_time),
            @"tcp_connect_duration": @(self.tcp_connect_time),
            @"ssl_handshake_duration": @(self.ssl_handshake_time),
            @"request_send_duration": @(self.request_send_time),
            @"time_to_first_byte": @(self.time_to_first_byte),
            @"response_receive_duration": @(self.response_receive_time),
            @"total_request_duration": @(self.total_request_time)
        },

        @"http_status_code": @(self.httpStatusCode),
        @"error_code": self.errorCode ?: @"",
        @"error_message": self.errorMessage ?: @"",
        @"response_size_bytes": @(self.responseSizeBytes),
        @"timestamp_sec": @(self.timestampSec)
    };
}

@end
