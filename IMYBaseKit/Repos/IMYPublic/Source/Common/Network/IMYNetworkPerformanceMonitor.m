//
//  IMYNetworkPerformanceMonitor.m
//  IMYPublic
//

#import "IMYNetworkPerformanceMonitor.h"
#import <IMYBaseKit/IMYPublic.h>

@implementation IMYNetworkPerformanceMonitor

+ (IMYNetworkPerformanceMetrics *)extractMetricsFromTaskMetrics:(NSURLSessionTaskMetrics *)metrics
                                                            task:(NSURLSessionTask *)task
                                                           error:(NSError *)error {
    IMYNetworkPerformanceMetrics *performanceMetrics = [[IMYNetworkPerformanceMetrics alloc] init];

    performanceMetrics.url = task.originalRequest.URL.absoluteString;
    performanceMetrics.method = task.originalRequest.HTTPMethod;

    if (error) {
        performanceMetrics.errorCode = [@(error.code) stringValue];
        performanceMetrics.errorMessage = error.localizedDescription;
    }

    if ([task.response isKindOfClass:[NSHTTPURLResponse class]]) {
        NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)task.response;
        performanceMetrics.httpStatusCode = httpResponse.statusCode;
    }

    if (task.response.expectedContentLength > 0) {
        performanceMetrics.responseSizeBytes = task.response.expectedContentLength;
    }

    for (NSURLSessionTaskTransactionMetrics *transMetric in metrics.transactionMetrics) {
        if (transMetric.domainLookupStartDate && transMetric.domainLookupEndDate) {
            performanceMetrics.dns_lookup_time = [transMetric.domainLookupEndDate timeIntervalSinceDate:transMetric.domainLookupStartDate] * 1000;
        }

        if (transMetric.connectStartDate && transMetric.connectEndDate) {
            performanceMetrics.tcp_connect_time = [transMetric.connectEndDate timeIntervalSinceDate:transMetric.connectStartDate] * 1000;
        }

        if (transMetric.secureConnectionStartDate && transMetric.secureConnectionEndDate) {
            performanceMetrics.ssl_handshake_time = [transMetric.secureConnectionEndDate timeIntervalSinceDate:transMetric.secureConnectionStartDate] * 1000;
        }

        if (transMetric.requestStartDate && transMetric.requestEndDate) {
            performanceMetrics.request_send_time = [transMetric.requestEndDate timeIntervalSinceDate:transMetric.requestStartDate] * 1000;
        }

        if (transMetric.requestStartDate && transMetric.responseStartDate) {
            performanceMetrics.time_to_first_byte = [transMetric.responseStartDate timeIntervalSinceDate:transMetric.requestStartDate] * 1000;
        }

        if (transMetric.responseStartDate && transMetric.responseEndDate) {
            performanceMetrics.response_receive_time = [transMetric.responseEndDate timeIntervalSinceDate:transMetric.responseStartDate] * 1000;
        }

        if (transMetric.fetchStartDate && transMetric.responseEndDate) {
            performanceMetrics.total_request_time = [transMetric.responseEndDate timeIntervalSinceDate:transMetric.fetchStartDate] * 1000;
        }

        break;
    }

    return performanceMetrics;
}

+ (void)logPerformanceMetrics:(IMYNetworkPerformanceMetrics *)metrics {
    NSLog(@"Network Performance - ID: %@", metrics.requestId);
    NSLog(@"URL: %@", metrics.url);
    NSLog(@"Method: %@", metrics.method);
    NSLog(@"Status: %ld", (long)metrics.httpStatusCode);
    NSLog(@"DNS: %.3fms, TCP: %.3fms, SSL: %.3fms, TTFB: %.3fms, Total: %.3fms",
          metrics.dns_lookup_time,
          metrics.tcp_connect_time,
          metrics.ssl_handshake_time,
          metrics.time_to_first_byte,
          metrics.total_request_time);
    
}

+ (void)reportPerformanceMetrics:(IMYNetworkPerformanceMetrics *)metrics {
    NSDictionary *reportData = [metrics toDictionary];
}

+ (void)handleTaskMetrics:(NSURLSessionTaskMetrics *)metrics
                     task:(NSURLSessionTask *)task
                    error:(NSError *)error {
    IMYNetworkPerformanceMetrics *performanceMetrics =
        [IMYNetworkPerformanceMonitor extractMetricsFromTaskMetrics:metrics task:task error:error];

    // 确保HTTP方法被正确设置
    if (!performanceMetrics.method || [performanceMetrics.method isEqualToString:@"Unknown"]) {
        NSString *httpMethod = [self extractHTTPMethodFromTask:task metrics:metrics];
        if (httpMethod) {
            performanceMetrics.method = httpMethod;
        }
    }

    [IMYNetworkPerformanceMonitor logPerformanceMetrics:performanceMetrics];
}

/**
 * 从 NSURLSessionTask 和 NSURLSessionTaskMetrics 中提取HTTP方法
 * @param task NSURLSessionTask 实例
 * @param metrics NSURLSessionTaskMetrics 实例
 * @return HTTP方法字符串
 */
+ (NSString *)extractHTTPMethodFromTask:(NSURLSessionTask *)task metrics:(NSURLSessionTaskMetrics *)metrics {
    NSString *httpMethod = nil;

    // 方法1: 从 task 的 originalRequest 获取
    if (task.originalRequest && task.originalRequest.HTTPMethod) {
        httpMethod = task.originalRequest.HTTPMethod;
    }
    // 方法2: 从 task 的 currentRequest 获取
    else if (task.currentRequest && task.currentRequest.HTTPMethod) {
        httpMethod = task.currentRequest.HTTPMethod;
    }
    // 方法3: 从 metrics 的 transactionMetrics 获取
    else if (metrics && metrics.transactionMetrics.count > 0) {
        NSURLSessionTaskTransactionMetrics *transactionMetric = metrics.transactionMetrics.firstObject;
        if (transactionMetric.request && transactionMetric.request.HTTPMethod) {
            httpMethod = transactionMetric.request.HTTPMethod;
        }
    }

    return httpMethod ?: @"Unknown";
}

@end
