# 网络诊断技术实现总结报告

## 1. 引言

为实现客户端应用性能监控（APM）体系中对网络质量的精确度量，本项目对 iOS `NSURLSession` 网络请求框架进行了深度、无侵入式的挂钩（Hook）。本次技术实践的目标是全面捕获所有通过 `NSURLSession` 发起的网络请求的详细性能指标（`NSURLSessionTaskMetrics`），无论其业务场景采用何种编码模式（Delegate模式或Completion Handler模式）。

## 2. 核心技术方案

方案的核心是利用 Objective-C 运行时的 `Method Swizzling` 机制，在应用启动的 `+load` 阶段，对 `NSURLSession` 的三个核心类方法进行拦截，并根据不同的场景采用不同的策略注入监控逻辑。

### 2.1 挂钩机制

我们对以下三个 `NSURLSession` 的类方法进行了 `Swizzle`：
- `+ sessionWithConfiguration:delegate:delegateQueue:`
- `+ sharedSession`
- `+ sessionWithConfiguration:`

这确保了无论业务层通过何种方式创建 `NSURLSession` 实例，其创建过程都会被我们的APM模块捕获。

### 2.2 Delegate 处理策略

针对 `NSURLSession` 的 `delegate` 存在与否及其具体实现，我们设计了两种应对策略：**动态方法注入**和**隐形代理**。

#### 2.2.1 有 Delegate 场景

当业务方在创建 `session` 时提供了 `delegate` 对象（包括 `sharedSession` 的内部 `delegate`），我们的逻辑如下：

1.  **运行时检查**：通过 `respondsToSelector:` 检查该 `delegate` 的类是否已经实现了目标性能回调方法 `URLSession:task:didFinishCollectingMetrics:`。

2.  **分类处理**：
    *   **若已实现**：我们采用**动态方法交换**（`method_exchangeImplementations`）的策略。我们首先通过 `class_addMethod` 将APM的监控方法（`imy_URLSession:...`）注入到 `delegate` 的类中，然后再与原始的系统回调方法进行实现交换。这确保了在执行我们的监控代码后，能够安全地调用回原始的业务逻辑。
    *   **若未实现**：我们采用**动态方法添加**（`class_addMethod`）的策略。直接将一个预设的、只包含APM监控逻辑的C函数（`imy_injected_didFinishCollectingMetrics`）作为 `URLSession:task:didFinishCollectingMetrics:` 的实现，动态添加到 `delegate` 的类中。

为保证性能和稳定性，我们使用关联对象（`objc_setAssociatedObject`）为每个类维护一个状态，确保对任何一个类的注入或交换操作在应用的生命周期内只执行一次。

#### 2.2.2 无 Delegate 场景

当业务方通过 `sessionWithConfiguration:` 创建会话，或在 `sessionWithConfiguration:delegate:delegateQueue:` 中显式传入 `delegate:nil` 时，`completionHandler` 模式将被使用。

在这种场景下，由于不存在 `delegate` 对象，我们无法通过修改类的方***现监控。因此，我们采用**隐形代理**（Invisible Proxy）策略：

1.  **创建代理实例**：我们创建一个 `IMYURLSessionDelegateProxy` 的实例。该实例内部实现了 `URLSession:task:didFinishCollectingMetrics:` 方法用于数据收集。
2.  **重定向调用**：我们将创建 `session` 的调用，重定向到 `sessionWithConfiguration:delegate:delegateQueue:` 方法，并将我们创建的 `proxy` 实例作为 `delegate` 参数传入。
3.  **消息转发**：`IMYURLSessionDelegateProxy` 通过重写 `forwardingTargetForSelector:`，实现了消息转发机制。这确保了如果苹果的内部实现需要向这个 `delegate` 发送其他消息，这些消息也能够被正确处理，从而避免了潜在的兼容性问题。

通过此策略，我们成功地在不影响 `completionHandler` 正常工作的前提下，为无 `delegate` 的 `session` 也安装了性能“探针”。

## 3. 结论

本方案通过结合 `Method Swizzling`、动态方法注入/交换和隐形代理三种运行时技术，实现了对 `NSURLSession` 网络请求的全面、无侵入式性能指标收集。该架构设计健壮，逻辑清晰，覆盖了所有主流的 `NSURLSession` 使用场景，为APM系统提供了稳定可靠的数据来源。
