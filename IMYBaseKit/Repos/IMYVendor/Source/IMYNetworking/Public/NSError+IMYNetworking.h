//
//  NSError+IMYNetworking.h
//  IMYNetworking
//
//  Created by mario on 16/4/15.
//  Copyright © 2016年 meiyou. All rights reserved.
//

#import <Foundation/Foundation.h>

@class IMYHTTPTask;

// 方便地获取 AFNetworking 的一些值

@interface NSError (IMYNetworking)

//
/// 这是原始请求的 URL
//
// set by IMYNetworking
@property (nonatomic, readonly) NSURL *imy_originalURL;

// set by IMYNetworking
@property (nonatomic, readonly) NSInteger imy_errorCode;

// set by IMYNetworking
@property (nonatomic, readonly) NSDictionary *imy_userInfo;

// set by IMYNetworking
@property (nonatomic, readonly) NSString *imyaf_userToken;

// set by IMYNetworking
@property (nonatomic, readonly) NSString *imyaf_virtualToken;

//
// 这是当前请求的 URL (HTTP 请求可能会有跳转，所以这个与原始的 URL 不一定相同)
//
// set by NS
@property (nonatomic, readonly) NSURL *ns_failingURL;

// set by AFNetworking
@property (nonatomic, readonly) NSData *af_responseData;

// set by AFNetworking
@property (nonatomic, readonly) NSHTTPURLResponse *af_httpResponse;

@end
