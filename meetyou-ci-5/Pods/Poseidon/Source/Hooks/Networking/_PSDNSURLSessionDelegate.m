//
//  _PSDNSURLSessionDelegate.m
//  Poseidon
//
//  Created by mario on 2018/1/11.
//  Copyright © 2018年 mario. All rights reserved.
//

#import "_PSDNSURLSessionDelegate.h"
#import "NSURLSessionTask+Poseidon.h"
#import "_PSDHTTPElement.h"
#import "_PSDHTTPCollector.h"
#import "_PSDUtils.h"

@implementation _PSDProxyNSURLSessionDelegate

- (instancetype)initWithObject:(_Nonnull id<NSURLSessionDelegate>)object {
    NSParameterAssert(object);
    _target = object;
    return self;
}

- (void)forwardInvocation:(NSInvocation *)invocation {
    [invocation invokeWithTarget:_target];
}

- (NSMethodSignature *)methodSignatureForSelector:(SEL)sel {
    return [_target methodSignatureForSelector:sel];
}

- (void)URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task didFinishCollectingMetrics:(NSURLSessionTaskMetrics *)metrics NS_AVAILABLE_IOS(10.0) {
    if (task._psd_http_element) {
        [_PSDHTTPCollector task:task didCollectMetrics:metrics];
    }
    if ([_target respondsToSelector:@selector(URLSession:task:didFinishCollectingMetrics:)]) {
        [_target URLSession:session task:task didFinishCollectingMetrics:metrics];
    }
}

- (void)URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task didCompleteWithError:(NSError *)error {
    if (task._psd_http_element) {
        NSTimeInterval time = _PSDGetCurrentTimestamp();
        [_PSDHTTPCollector task:task didCompleteAtTime:time response:task.response error:error data:nil];
    }
    if ([_target respondsToSelector:@selector(URLSession:task:didCompleteWithError:)]) {
        [_target URLSession:session task:task didCompleteWithError:error];
    }
}

- (void)URLSession:(NSURLSession *)session dataTask:(NSURLSessionDataTask *)dataTask didReceiveResponse:(NSURLResponse *)response completionHandler:(void (^)(NSURLSessionResponseDisposition))completionHandler {
    if (dataTask._psd_http_element) {
        NSTimeInterval time = _PSDGetCurrentTimestamp();
        [_PSDHTTPCollector task:dataTask didReceiveResponse:response atTime:time];
    }
    if ([_target respondsToSelector:@selector(URLSession:dataTask:didReceiveResponse:completionHandler:)]) {
        [_target URLSession:session dataTask:dataTask didReceiveResponse:response completionHandler:completionHandler];
    }
}

@end

#pragma mark - _PSDConcreteNSURLSessionDelegate

@implementation _PSDConcreteNSURLSessionDelegate

- (void)URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task didFinishCollectingMetrics:(NSURLSessionTaskMetrics *)metrics NS_AVAILABLE_IOS(10.0) {
    if (task._psd_http_element) {
        [_PSDHTTPCollector task:task didCollectMetrics:metrics];
    }
}

- (void)URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task didCompleteWithError:(NSError *)error {
    if (task._psd_http_element) {
        NSTimeInterval time = _PSDGetCurrentTimestamp();
        [_PSDHTTPCollector task:task didCompleteAtTime:time response:task.response error:error data:nil];
    }
}

- (void)URLSession:(NSURLSession *)session dataTask:(NSURLSessionDataTask *)dataTask didReceiveResponse:(NSURLResponse *)response completionHandler:(void (^)(NSURLSessionResponseDisposition))completionHandler {
    if (dataTask._psd_http_element) {
        NSTimeInterval time = _PSDGetCurrentTimestamp();
        [_PSDHTTPCollector task:dataTask didReceiveResponse:response atTime:time];
    }
    if (completionHandler) {
        completionHandler(NSURLSessionResponseAllow);
    }
}

@end

#pragma mark - _PSDDummyNSURLSessionDelegate

@implementation _PSDDummyNSURLSessionDelegate

- (void)URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task didFinishCollectingMetrics:(NSURLSessionTaskMetrics *)metrics NS_AVAILABLE_IOS(10.0) {
}

- (void)URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task didCompleteWithError:(NSError *)error {
}

- (void)URLSession:(NSURLSession *)session dataTask:(NSURLSessionDataTask *)dataTask didReceiveResponse:(NSURLResponse *)response completionHandler:(void (^)(NSURLSessionResponseDisposition))completionHandler {
    if (completionHandler) {
        completionHandler(NSURLSessionResponseAllow);
    }
}

@end
